<template>
  <div ref="propertyPanel" class="property-panel">
    <div v-if="nodeName" class="node-name">{{ nodeName }}</div>
     <component
      :is="getComponent"
      v-if="element"
      :element="element"
      :modeler="modeler"
      :users="users"
      :groups="groups"
      :exps="exps"
      :categorys="categorys"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import taskPanel from './components/nodePanel/task'
import startEndPanel from './components/nodePanel/startEnd'
import processPanel from './components/nodePanel/process'
import sequenceFlowPanel from './components/nodePanel/sequenceFlow'
import gatewayPanel from './components/nodePanel/gateway'
import { NodeName } from './lang/zh'

const props = defineProps({
  users: {
    type: Array,
    required: true
  },
  groups: {
    type: Array,
    required: true
  },
  categorys: {
    type: Array,
    required: true
  },
  exps: {
    type: Array,
    default: () => []
  },
  modeler: {
    type: Object,
    required: true
  }
})

const propertyPanel = ref(null)
const element = ref(null)
const form = ref({
  id: '',
  name: '',
  color: null
})

const getComponent = computed(() => {
  const type = element.value?.type
  if (['bpmn:IntermediateThrowEvent', 'bpmn:StartEvent', 'bpmn:EndEvent'].includes(type)) {
    return startEndPanel
  }
  if ([
    'bpmn:UserTask',
    'bpmn:Task',
    'bpmn:SendTask',
    'bpmn:ReceiveTask',
    'bpmn:ManualTask',
    'bpmn:BusinessRuleTask',
    'bpmn:ServiceTask',
    'bpmn:ScriptTask'
  ].includes(type)) {
    return taskPanel
  }
  if (type === 'bpmn:SequenceFlow') {
    return sequenceFlowPanel
  }
  if ([
    'bpmn:InclusiveGateway',
    'bpmn:ExclusiveGateway',
    'bpmn:ParallelGateway',
    'bpmn:EventBasedGateway'
  ].includes(type)) {
    return gatewayPanel
  }
  if (type === 'bpmn:Process') {
    return processPanel
  }
  return null
})

const nodeName = computed(() => {
  if (element.value) {
    const bizObj = element.value.businessObject
    const type = bizObj?.eventDefinitions
      ? bizObj.eventDefinitions[0].$type
      : bizObj.$type
    return NodeName[type] || type
  }
  return ''
})

const handleModeler = () => {
  props.modeler.on('root.added', e => {
    if (e.element.type === 'bpmn:Process') {
      element.value = null
      nextTick().then(() => {
        element.value = e.element
      })
    }
  })
  props.modeler.on('element.click', e => {
    const { element: clickedElement } = e
    if (clickedElement.type === 'bpmn:Process'
      || clickedElement.type === 'bpmn:SequenceFlow'
      || clickedElement.type === 'bpmn:EndEvent') {
      nextTick().then(() => {
        element.value = clickedElement
      })
    }
  })
  props.modeler.on('selection.changed', e => {
    element.value = null
    const selectedElement = e.newSelection[0]
    if (selectedElement) {
      nextTick().then(() => {
        element.value = selectedElement
      })
    }
  })
}

onMounted(() => {
  handleModeler()
})
</script>

<style lang="scss">
.property-panel {
  padding: 20px 20px;

  // reset element css
  .el-form--label-top .el-form-item__label {
    padding: 0;
  }

  // 设置设计器右侧表单高度
  .el-form-item {
    margin-bottom: 6px;
  }

  .tab-table .el-form-item {
    margin-bottom: 16px;
  }

  .node-name {
    border-bottom: 1px solid #ccc;
    padding: 0 0 10px 20px;
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: bold;
    color: #444;
  }
}
</style>
