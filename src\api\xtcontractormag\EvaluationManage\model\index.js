import request from '@/utils/request'

// 查询评价模型定义列表
export function listModel(query) {
  return request({
    url: '/contractor/model',
    method: 'get',
    params: query
  })
}

// 查询评价模型定义详细
export function getModel(id) {
  return request({
    url: '/contractor/model/' + id,
    method: 'get'
  })
}

// 新增评价模型定义
export function addModel(data) {
  return request({
    url: '/contractor/model',
    method: 'post',
    data: data
  })
}

// 修改评价模型定义
export function updateModel(data) {
  return request({
    url: '/contractor/model',
    method: 'post',
    data: data
  })
}

// 删除评价模型定义
export function delModel(data) {
  return request({
    url: '/contractor/model/',
    method: 'delete',
    data: data
  })
}
