<template>
  <div v-loading="isView" class="flow-containers" :class="{ 'view-mode': isView }">
    <el-container style="height: 100%">
      <el-header style="border-bottom: 1px solid rgb(218 218 218);height: auto;padding-left:0px">
        <div style="display: flex; padding: 10px 0px; justify-content: space-between;">
          <div>
            <el-upload action="" :before-upload="openBpmn" style="margin-right: 10px; display:inline-block;">
              <el-tooltip effect="dark" content="加载xml" placement="bottom">
                <el-button size="mini" icon="el-icon-folder-opened">加载xml</el-button>
              </el-tooltip>
            </el-upload>
            <el-tooltip effect="dark" content="新建" placement="bottom">
              <el-button size="mini" icon="el-icon-circle-plus" @click="newDiagram">新建</el-button>
            </el-tooltip>
            <el-tooltip effect="dark" content="自适应屏幕" placement="bottom">
              <el-button size="mini" icon="el-icon-full-screen" @click="fitViewport">自适应</el-button>
            </el-tooltip>
            <el-tooltip effect="dark" content="放大" placement="bottom">
              <el-button size="mini" icon="el-icon-zoom-in" @click="zoomViewport(true)">放大</el-button>
            </el-tooltip>
            <el-tooltip effect="dark" content="缩小" placement="bottom">
              <el-button size="mini" icon="el-icon-zoom-out" @click="zoomViewport(false)">缩小</el-button>
            </el-tooltip>
            <el-tooltip effect="dark" content="后退" placement="bottom">
              <el-button size="mini" icon="el-icon-back" @click="modeler.get('commandStack').undo()">后退</el-button>
            </el-tooltip>
            <el-tooltip effect="dark" content="前进" placement="bottom">
              <el-button size="mini" icon="el-icon-right" @click="modeler.get('commandStack').redo()">前进</el-button>
            </el-tooltip>
          </div>
          <div>
            <!--            <el-button size="mini" icon="el-icon-s-check" @click="verifyXML">校验xml</el-button>-->
            <el-button size="mini" icon="el-icon-view" @click="showXML">查看xml</el-button>
            <el-button size="mini" icon="el-icon-download" @click="saveXML(true)">下载xml</el-button>
            <el-button size="mini" icon="el-icon-picture" @click="saveImg('svg', true)">下载svg</el-button>
            <el-button size="mini" type="primary" @click="save">保存模型</el-button>
          </div>
        </div>
      </el-header>
      <el-container style="align-items: stretch">
        <el-main style="padding: 0;">
          <div ref="canvas" class="canvas" />
        </el-main>
        <el-aside style="width: 400px; min-height: 650px; background-color: #f0f2f5">
          <panel v-if="modeler" :modeler="modeler" :users="users" :groups="groups" :exps="exps" :categorys="categorys" />
        </el-aside>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
// 汉化
import customTranslate from './common/customTranslate'
import lintModule from 'bpmn-js-bpmnlint'
import Modeler from 'bpmn-js/lib/Modeler'
// import bpmnlintConfig from './.bpmnlintrc'
import panel from './PropertyPanel'
import getInitStr from './flowable/init'
// 引入flowable的节点文件
import FlowableModule from './flowable/flowable.json'
import customControlsModule from './customPanel'

// 定义 props
const props = defineProps({
  xml: {
    type: String,
    default: ''
  },
  users: {
    type: Array,
    default: () => []
  },
  groups: {
    type: Array,
    default: () => []
  },
  categorys: {
    type: Array,
    default: () => []
  },
  exps: {
    type: Array,
    default: () => []
  },
  isView: {
    type: Boolean,
    default: false
  }
})

// 定义 emit
const emit = defineEmits(['showXML', 'save'])

// 定义 ref
const modeler = ref(null)
const zoom = ref(1)

// 监听 xml 变化
watch(() => props.xml, (val) => {
  if (val) {
    createNewDiagram(val)
  }
})

// 创建 modeler 实例
onMounted(() => {
  modeler.value = new Modeler({
    container: document.querySelector('.canvas'),
    additionalModules: [
      lintModule,
      customControlsModule,
      { // 汉化
        translate: ['value', customTranslate]
      }
    ],
    // 去除流程校验器,有需求可自行添加,需要在package.json 加入 "bpmnlint-plugin-local": "file:bpmnlint-plugin-local"
    // linting: {
    //   bpmnlint: bpmnlintConfig
    // },
    moddleExtensions: {
      flowable: FlowableModule
    }
  })

  // 新增流程定义
  if (!props.xml) {
    newDiagram()
  } else {
    createNewDiagram(props.xml)
  }
})

// 新建流程图
const newDiagram = () => {
  createNewDiagram(getInitStr())
}

// 自适应屏幕
const fitViewport = () => {
  zoom.value = modeler.value.get('canvas').zoom('fit-viewport')
  const bbox = document.querySelector('.flow-containers .viewport').getBBox()
  const currentViewbox = modeler.value.get('canvas').viewbox()
  const elementMid = {
    x: bbox.x + bbox.width / 2 - 65,
    y: bbox.y + bbox.height / 2
  }
  modeler.value.get('canvas').viewbox({
    x: elementMid.x - currentViewbox.width / 2,
    y: elementMid.y - currentViewbox.height / 2,
    width: currentViewbox.width,
    height: currentViewbox.height
  })
  zoom.value = bbox.width / currentViewbox.width * 1.8
}

// 放大缩小
const zoomViewport = (zoomIn = true) => {
  zoom.value = modeler.value.get('canvas').zoom()
  zoom.value += zoomIn ? 0.1 : -0.1
  modeler.value.get('canvas').zoom(zoom.value)
}

// 创建新的流程图
const createNewDiagram = async (data) => {
  // 将字符串转换成图显示出来
  // data = data.replace(/<!\[CDATA\[(.+?)]]>/g, '&lt;![CDATA[$1]]&gt;')
  data = data.replace(/<!\[CDATA\[(.+?)]]>/g, (match, str) => {
    return str.replace(/</g, '&lt;')
  })
  try {
    await modeler.value.importXML(data)
    // this.adjustPalette()
    fitViewport()
  } catch (err) {
    console.error(err.message, err.warnings)
  }
}

// 获取流程信息
const getProcess = () => {
  const element = getProcessElement()
  return {
    id: element.id,
    name: element.name,
    category: element.$attrs['flowable:processCategory']
  }
}

// 获取流程元素
const getProcessElement = () => {
  const rootElements = modeler.value.getDefinitions().rootElements
  for (let i = 0; i < rootElements.length; i++) {
    if (rootElements[i].$type === 'bpmn:Process') return rootElements[i]
  }
}

// 校验 XML
// const verifyXML = async () => {
//   const linting = modeler.value.get('linting')
//   linting.toggle()
// }

// 保存 XML
const saveXML = async (download = false) => {
  try {
    const { xml } = await modeler.value.saveXML({ format: true })
    if (download) {
      downloadFile(`${getProcessElement().name}.bpmn20.xml`, xml, 'application/xml')
    }
    return xml
  } catch (err) {
    console.log(err)
  }
}

// 查看 XML
const showXML = async () => {
  try {
    const xml = await saveXML()
    emit('showXML', xml)
  } catch (err) {
    console.log(err)
  }
}

// 保存图片
const saveImg = async (type = 'svg', download = false) => {
  try {
    const { svg } = await modeler.value.saveSVG({ format: true })
    if (download) {
      downloadFile(getProcessElement().name, svg, 'image/svg+xml')
    }
    return svg
  } catch (err) {
    console.log(err)
  }
}

// 保存流程图
const save = async () => {
  const process = getProcess()
  const xml = await saveXML()
  const svg = await saveImg()
  const result = { process, xml, svg }
  emit('save', result)
  window.parent.postMessage(result, '*')
}

// 打开 BPMN 文件
const openBpmn = (file) => {
  const reader = new FileReader()
  reader.readAsText(file, 'utf-8')
  reader.onload = () => {
    createNewDiagram(reader.result)
  }
  return false
}

// 下载文件
const downloadFile = (filename, data, type) => {
  const a = document.createElement('a')
  const url = window.URL.createObjectURL(new Blob([data], { type: type }))
  a.href = url
  a.download = filename
  a.click()
  window.URL.revokeObjectURL(url)
}
</script>

<style lang="scss">
/*左边工具栏以及编辑节点的样式*/
@import 'bpmn-js/dist/assets/diagram-js.css';
@import "bpmn-js/dist/assets/bpmn-font/css/bpmn.css";
@import "bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css";
@import "bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css";
//@import "~bpmn-js-bpmnlint/dist/assets/css/bpmn-js-bpmnlint.css";
.view-mode {
  .el-header, .el-aside, .djs-palette, .bjs-powered-by {
    display: none;
  }
  .el-loading-mask {
    background-color: initial;
  }
  .el-loading-spinner {
    display: none;
  }
}
.flow-containers {
  width: 100%;
  height: 100%;
  .canvas {
    width: 100%;
    height: 100%;
  }
  .panel {
    position: absolute;
    right: 0;
    top: 50px;
    width: 300px;
  }
  .load {
    margin-right: 10px;
  }
  .el-form-item__label {
    font-size: 13px;
  }

  .djs-palette {
    left: 0px !important;
    top: 0px;
    border-top: none;
  }

  .highlight.djs-shape .djs-visual > :nth-child(1) {
    fill: green !important;
    stroke: green !important;
    fill-opacity: 0.2 !important;
  }
  .highlight.djs-shape .djs-visual > :nth-child(2) {
    fill: green !important;
  }
  .highlight.djs-shape .djs-visual > path {
    fill: green !important;
    fill-opacity: 0.2 !important;
    stroke: green !important;
  }
  .highlight.djs-connection > .djs-visual > path {
    stroke: green !important;
  }

  .highlight-todo.djs-connection > .djs-visual > path {
    stroke: orange !important;
    stroke-dasharray: 4px !important;
    fill-opacity: 0.2 !important;
  }
  .highlight-todo.djs-shape .djs-visual > :nth-child(1) {
    fill: orange !important;
    stroke: orange !important;
    stroke-dasharray: 4px !important;
    fill-opacity: 0.2 !important;
  }
  .overlays-div {
    font-size: 10px;
    color: red;
    width: 100px;
    top: -20px !important;
  }
}
</style>
