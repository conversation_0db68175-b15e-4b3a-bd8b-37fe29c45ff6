import request from '@/utils/request'

// 查询评价评分结果（自动和人工）列表
export function listEvaluationScore(query) {
  return request({
    url: '/contractor/evaluationScore',
    method: 'get',
    params: query
  })
}

// 查询评价评分结果（自动和人工）详细
export function getEvaluationScore(id) {
  return request({
    url: '/contractor/evaluationScore/' + id,
    method: 'get'
  })
}

// 新增评价评分结果（自动和人工）
export function addEvaluationScore(data) {
  return request({
    url: '/contractor/evaluationScore',
    method: 'post',
    data: data
  })
}

// 修改评价评分结果（自动和人工）
export function updateEvaluationScore(data) {
  return request({
    url: '/contractor/evaluationScore',
    method: 'post',
    data: data
  })
}

// 删除评价评分结果（自动和人工）
export function delEvaluationScore(data) {
  return request({
    url: '/contractor/evaluationScore/',
    method: 'delete',
    data: data
  })
}
