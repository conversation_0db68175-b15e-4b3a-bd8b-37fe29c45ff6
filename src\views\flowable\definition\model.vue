<template>
  <div>
    <bpmn-modeler
      ref="refNode"
      :xml="xml"
      :users="users"
      :groups="groups"
      :categorys="sys_process_category"
      :exps="exps"
      :is-view="false"
      @save="save"
      @showXML="showXML"
    />
    <!--在线查看xml-->
    <el-drawer :title="xmlTitle" :modal="false" direction="rtl" v-model="xmlOpen" size="60%">
      <!-- 设置对话框内容高度 -->
        <el-scrollbar>
            <pre><code class="xml" v-highlight="xmlData"></code></pre>
        </el-scrollbar>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { readXml, roleList, saveXml, userList, expList } from "@/api/flowable/definition";
import bpmnModeler from '@/components/Process/index';
import vkBeautify from 'vkbeautify';
import hljs from 'highlight.js';

// 自定义指令
const highlight = {
  deep: true,
  bind(el, binding) {
    const targets = el.querySelectorAll('code');
    targets.forEach(target => {
      if (typeof binding.value === 'string') {
        target.textContent = binding.value;
      }
      hljs.highlightBlock(target);
    });
  },
  componentUpdated(el, binding) {
    const targets = el.querySelectorAll('code');
    targets.forEach(target => {
      if (typeof binding.value === 'string') {
        target.textContent = binding.value;
        hljs.highlightBlock(target);
      }
    });
  },
};

// 注册自定义指令
const app = {
  directives: {
    highlight,
  },
};

// 定义响应式变量
const xml = ref("");
const xmlOpen = ref(false);
const xmlTitle = ref('');
const xmlData = ref('');
const users = ref([]);
const groups = ref([]);
const { proxy } = getCurrentInstance();
const { sys_process_category } = proxy.useDict("sys_process_category");
const exps = ref([]);

// 获取路由和路由实例
const route = useRoute();
const router = useRouter();

// 使用 Composition API 的生命周期函数
onMounted(() => {
  const deployId = route.query && route.query.deployId;
  // 查询流程xml
  if (deployId) {
    getXmlData(deployId);
  }
  getDataList();
});

// 定义方法
/** xml 文件 */
const getXmlData = (deployId) => {
  // 发送请求，获取xml
  readXml(deployId).then(res => {
    xml.value = res.data;
  });
};

/** 保存xml */
const save = (data) => {
  const params = {
    name: data.process.name,
    category: data.process.category,
    xml: data.xml,
  };
  saveXml(params).then(res => {
    // 这里直接使用中文显示即可
    alert('保存成功: ' + res.msg)
    // 关闭当前标签页并返回上个页面
    // 这里假设你已经有了相应的方法来处理标签页关闭，Vue3 中标签页管理方式可能有所不同
    // this.$store.dispatch("tagsView/delView", this.$route);
    // this.$router.go(-1)
    router.go(-1);
  });
};

/** 指定流程办理人员列表 */
const getDataList = () => {
  userList().then(res => {
    users.value = res.data.map(val => ({
      ...val,
      userId: val.userId.toString(),
    }));
  });
  roleList().then(res => {
    console.log(res)
    groups.value = res.rows.map(val => ({
      ...val,
      roleId: val.roleId.toString(),
    }));
  });
  expList().then(res => {
    exps.value = res.data;
  });
};

/** 展示xml */
const showXML = (xmlData) => {
  xmlTitle.value = 'xml查看';
  xmlOpen.value = true;
  xmlData.value = vkBeautify.xml(xmlData);
};
</script>

<style lang="scss" scoped>
:deep(.el-main){
  background: #fff;
}
:deep(.el-container){
  height: 88vh;
}
:deep(.el-header){
  padding: 0;
}

// 修改对话框高度
.showAll_dialog {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  ::v-deep .el-dialog {
    margin: 0 auto !important;
    height: 80%;
    overflow: hidden;
    background-color: #ffffff;

    .el-dialog__body {
      position: absolute;
      left: 0;
      top: 54px;
      bottom: 0;
      right: 0;
      z-index: 1;
      overflow: hidden;
      overflow-y: auto;
      // 下边设置字体，我的需求是黑底白字
      color: #ffffff;
      padding: 0 15px;
    }
  }
}
</style>
