import request from '@/utils/request'

// 查询阶段评价记录列表
export function listPhaseRecord(query) {
  return request({
    url: '/contractor/phaseRecord',
    method: 'get',
    params: query
  })
}

// 查询阶段评价记录详细
export function getPhaseRecord(id) {
  return request({
    url: '/contractor/phaseRecord/' + id,
    method: 'get'
  })
}

// 新增阶段评价记录
export function addPhaseRecord(data) {
  return request({
    url: '/contractor/phaseRecord',
    method: 'post',
    data: data
  })
}

// 修改阶段评价记录
export function updatePhaseRecord(data) {
  return request({
    url: '/contractor/phaseRecord',
    method: 'post',
    data: data
  })
}

// 删除阶段评价记录
export function delPhaseRecord(data) {
  return request({
    url: '/contractor/phaseRecord/',
    method: 'delete',
    data: data
  })
}
