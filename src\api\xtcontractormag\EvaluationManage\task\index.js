import request from '@/utils/request'

// 查询评价任务记录列表
export function listTask(query) {
  return request({
    url: '/contractor/task',
    method: 'get',
    params: query
  })
}

// 查询评价任务记录详细
export function getTask(id) {
  return request({
    url: '/contractor/task/' + id,
    method: 'get'
  })
}

// 新增评价任务记录
export function addTask(data) {
  return request({
    url: '/contractor/task',
    method: 'post',
    data: data
  })
}

// 修改评价任务记录
export function updateTask(data) {
  return request({
    url: '/contractor/task',
    method: 'post',
    data: data
  })
}

// 删除评价任务记录
export function delTask(data) {
  return request({
    url: '/contractor/task/',
    method: 'delete',
    data: data
  })
}
