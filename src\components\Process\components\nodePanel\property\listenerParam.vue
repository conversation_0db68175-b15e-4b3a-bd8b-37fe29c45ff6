<template>
  <div>
    <el-dialog
      title="监听器参数"
      v-model="dialogVisible"
      width="700px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      @closed="emit('close', formData.paramList)"
    >
      <x-form ref="xFormRef" v-model="formData" :config="formConfig" />
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" size="medium" @click="closeDialog">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { xForm } from 'xcrud'
import mixinXcrudConfig from '../../../common/mixinXcrud'

// 应用mixin中的全局配置
mixinXcrudConfig()

const props = defineProps({
  value: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['close'])

const dialogVisible = ref(true)
const formData = ref({
  paramList: props.value
})
const xFormRef = ref(null)

const formConfig = computed(() => ({
  inline: false,
  item: [
    {
      xType: 'tabs',
      tabs: [
        {
          label: '监听器参数',
          name: 'paramList',
          column: [
            {
              label: '类型',
              name: 'type',
              width: 180,
              rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
              xType: 'select',
              dic: [
                { label: '字符串', value: 'stringValue' },
                { label: '表达式', value: 'expression' }
              ]
            },
            {
              label: '名称',
              name: 'name',
              width: 180,
              rules: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
              xType: 'input'
            },
            {
              label: '值',
              name: 'value',
              xType: 'input',
              rules: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]
            }
          ]
        }
      ]
    }
  ]
}))

const closeDialog = () => {
  xFormRef.value.validate().then(() => {
    dialogVisible.value = false
  }).catch(e => console.error(e))
}
</script>

<style>
.flow-containers  .el-badge__content.is-fixed {
  top: 18px;
}
</style>
