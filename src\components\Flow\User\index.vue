<template>
  <div>
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="deptName"
            placeholder="请输入部门名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="deptOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            node-key="id"
            :default-expand-all="false"
            highlight-current
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="16" :xs="24">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="用户账号" prop="userName">
            <el-input
              v-model="queryParams.userName"
              placeholder="请输入用户账号"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="用户姓名" prop="nickName">
            <el-input
              v-model="queryParams.nickName"
              placeholder="请输入用户姓名"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t('common.search') }}</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{ $t('common.reset') }}</el-button>
          </el-form-item>
        </el-form>
        <el-table v-show="checkType === 'multiple'" ref="dataTable" v-loading="loading" :data="userList"
        @selection-change="handleMultipleUserSelect"
        :row-key="row => row.userId"
        >
          <el-table-column type="selection" :reserve-selection="true" width="50" align="center" />
          <el-table-column label="用户编号" align="center" key="userId" prop="userId" v-if="columns[0].visible" />
          <el-table-column label="登录账号" align="center" key="userName" prop="userName" v-if="columns[1].visible" :show-overflow-tooltip="true" />
          <el-table-column label="用户姓名" align="center" key="nickName" prop="nickName" v-if="columns[2].visible" :show-overflow-tooltip="true" />
          <el-table-column label="部门" align="center" key="deptName" prop="dept.deptName" v-if="columns[3].visible" :show-overflow-tooltip="true" />
        </el-table>
        <el-table v-show="checkType === 'single'" v-loading="loading" :data="userList" @current-change="handleSingleUserSelect">
          <el-table-column width="55" align="center">
            <template #default="scope">
              <el-radio v-model="radioSelected" :label="scope.row.userId">{{''}}</el-radio>
            </template>
          </el-table-column>
          <el-table-column label="用户编号" align="center" key="userId" prop="userId" v-if="columns[0].visible" />
          <el-table-column label="登录账号" align="center" key="userName" prop="userName" v-if="columns[1].visible" :show-overflow-tooltip="true" />
          <el-table-column label="用户姓名" align="center" key="nickName" prop="nickName" v-if="columns[2].visible" :show-overflow-tooltip="true" />
          <el-table-column label="部门" align="center" key="deptName" prop="dept.deptName" v-if="columns[3].visible" :show-overflow-tooltip="true" />
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
      <el-col :span="4" :xs="24" v-show="checkType === 'multiple'">
        <h6>已选人员</h6>
        <el-divider></el-divider>
        <el-tag v-for="(user,index) in userData" :key="index" closable @close="handleClose(user)">
          {{user.nickName}}
        </el-tag>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue';
import { listUser, deptTreeSelect } from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { StrUtil } from "@/utils/StrUtil";

const { proxy } = getCurrentInstance();
const { sys_normal_disable, sys_user_sex } = proxy.useDict('sys_normal_disable', 'sys_user_sex');

// 接受父组件的值
const props = defineProps({
  // 回显数据传值
  selectValues: {
    type: [Number, String, Array],
    default: null,
    required: false,
  },
  // 表格类型
  checkType: {
    type: String,
    default: 'multiple',
    required: true,
  },
});

const emit = defineEmits(['handleUserSelect']);

// 遮罩层
const loading = ref(true);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 用户表格数据
const userList = ref([]);
// 已选用户数据
const userData = ref([]);
// 弹出层标题
const title = ref("");
// 部门树选项
const deptOptions = ref(undefined);
// 是否显示弹出层
const open = ref(false);
// 部门名称
const deptName = ref(undefined);
// 表单参数
const form = ref({});
const defaultProps = reactive({
  children: "children",
  label: "label"
});
// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 5,
  userName: undefined,
  nickName: undefined,
  status: undefined,
  deptId: undefined
});
// 列信息
const columns = ref([
  { key: 0, label: `用户编号`, visible: true },
  { key: 1, label: `用户名称`, visible: true },
  { key: 2, label: `用户昵称`, visible: true },
  { key: 3, label: `部门`, visible: true },
  { key: 4, label: `手机号码`, visible: true },
  { key: 5, label: `状态`, visible: true },
  { key: 6, label: `创建时间`, visible: true }
]);
const radioSelected = ref(null); // 单选框传值
const selectUserList = ref([]); // 回显数据传值

// 根据名称筛选部门树
watch(deptName, (val) => {
  tree.value.filter(val);
});

watch(selectValues, (newVal) => {
  if (StrUtil.isNotBlank(newVal)) {
    if (newVal instanceof Number) {
      radioSelected.value = newVal;
    } else {
      selectUserList.value = newVal;
    }
  }
}, { immediate: true });

// 查询用户列表
const getList = () => {
  loading.value = true;
  listUser(queryParams).then(response => {
    userList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
};

// 查询部门下拉树结构
const getDeptTree = () => {
  deptTreeSelect().then(response => {
    deptOptions.value = response.data;
  });
};

// 多选框选中数据
const handleMultipleUserSelect = (selection) => {
  if (selection.length > 0) {
    userData.value = selection;
    emit('handleUserSelect', selection);
  }
};

// 单选框选中数据
const handleSingleUserSelect = (selection) => {
  //点击当前行时,radio同样有选中效果
  radioSelected.value = selection.userId;
  emit('handleUserSelect', selection);
};

// 关闭标签
const handleClose = (tag) => {
  userData.value.splice(userData.value.indexOf(tag), 1);
  dataTable.value.toggleRowSelection(tag, false);
};

// 筛选节点
const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};

// 节点单击事件
const handleNodeClick = (data) => {
  queryParams.deptId = data.id;
  handleQuery();
};

// 搜索按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

// 重置按钮操作
const resetQuery = () => {
  queryParams.deptId = undefined;
  tree.value.setCurrentKey(null);
  handleQuery();
};

onMounted(() => {
  getList();
  getDeptTree();
});
</script>

<style scoped>
</style>
