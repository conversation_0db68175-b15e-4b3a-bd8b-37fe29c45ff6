<template>
    <div class="app-container">
    <el-row>
      <el-col :span="20" :xs="24">
      <el-form :model="queryParams" ref="queryFormRef" size="small" :inline="true" v-show="showSearch">
        <el-form-item label="角色名称" prop="roleName">
          <el-input
            v-model="queryParams.roleName"
            placeholder="请输入角色名称"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t('common.search') }}</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{ $t('common.reset') }}</el-button>
        </el-form-item>
      </el-form>

      <el-table v-show="checkType === 'multiple'" ref="dataTableRef" v-loading="loading" :data="roleList" @selection-change="handleMultipleRoleSelect">
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="角色编号" prop="roleId" width="120" />
        <el-table-column label="角色名称" prop="roleName" :show-overflow-tooltip="true" width="150" />
        <el-table-column label="权限字符" prop="roleKey" :show-overflow-tooltip="true" width="150" />
        <el-table-column label="显示顺序" prop="roleSort" width="100" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-table v-show="checkType === 'single'" v-loading="loading" :data="roleList" @current-change="handleSingleRoleSelect">
        <el-table-column  width="55" align="center" >
          <template #default="scope">
            <!-- 可以手动的修改label的值，从而控制选择哪一项 -->
            <el-radio v-model="radioSelected" :label="scope.row.roleId">{{''}}</el-radio>
          </template>
        </el-table-column>
        <el-table-column label="角色编号" prop="roleId" width="120" />
        <el-table-column label="角色名称" prop="roleName" :show-overflow-tooltip="true" width="150" />
        <el-table-column label="权限字符" prop="roleKey" :show-overflow-tooltip="true" width="150" />
        <el-table-column label="显示顺序" prop="roleSort" width="100" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
      </el-table>

      <pagination
          v-show="total>0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />

    </el-col>
      <el-col :span="4" :xs="24">
            <h6>已选角色</h6>
            <el-divider></el-divider>
            <el-tag v-for="(role,index) in roleData" :key="index" closable @close="handleClose(role)">
              {{role.roleName}}
            </el-tag>
        </el-col>
      </el-row>
    </div>
  </template>

<script setup>
import { ref, reactive, watch, onMounted, getCurrentInstance } from 'vue'
import { listRole } from "@/api/system/role"
import { StrUtil } from "@/utils/StrUtil"

// 获取当前实例
const { proxy } = getCurrentInstance()
const { parseTime } = proxy
const { $t } = proxy

// 定义 props
const props = defineProps({
  // 回显数据传值
  selectValues: {
    type: [Number, String, Array],
    default: null,
    required: false
  },
  checkType: {
    type: String,
    default: 'multiple',
    required: false
  }
})

// 定义 emits
const emit = defineEmits(['handleRoleSelect'])

// 定义 refs
const queryFormRef = ref()
const dataTableRef = ref()

// 响应式数据
const loading = ref(true)
const showSearch = ref(true)
const total = ref(0)
const roleList = ref([])
const roleData = ref([])
const radioSelected = ref(null)
const selectRoleList = ref([])

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 5,
  roleName: undefined,
  roleKey: undefined,
  status: undefined
})

// 监听 selectValues 变化
watch(() => props.selectValues, (newVal) => {
  if (StrUtil.isNotBlank(newVal)) {
    if (typeof newVal === 'number' || typeof newVal === 'string') {
      radioSelected.value = newVal
    } else {
      selectRoleList.value = newVal
    }
  }
}, { immediate: true })

// 查询角色列表
const getList = () => {
  loading.value = true
  listRole(queryParams).then(response => {
    roleList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 多选框选中数据
const handleMultipleRoleSelect = (selection) => {
  if (selection.length > 0) {
    roleData.value = selection
    const idList = selection.map(item => item.roleId)
    const nameList = selection.map(item => item.roleName)
    emit('handleRoleSelect', idList.join(','), nameList.join(','))
  }
}

// 单选框选中数据
const handleSingleRoleSelect = (selection) => {
  radioSelected.value = selection.roleId
  const roleName = selection.roleName
  emit('handleRoleSelect', radioSelected.value.toString(), roleName)
}

// 关闭标签
const handleClose = (tag) => {
  roleData.value.splice(roleData.value.indexOf(tag), 1)
  dataTableRef.value.toggleRowSelection(tag, false)
}

// 搜索按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置按钮操作
const resetQuery = () => {
  handleQuery()
}

// 组件挂载后执行
onMounted(() => {
  getList()
})
</script>
  <style>
  </style>
