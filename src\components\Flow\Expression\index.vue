<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入表达式名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in sys_common_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="expressionList" row-key="id" @current-change="handleSingleExpSelect">
      <el-table-column width="55" align="center">
        <template v-slot:default="scope">
          <el-radio v-model="radioSelected" :label="scope.row.id">{{''}}</el-radio>
        </template>
      </el-table-column>
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="名称" align="center" prop="name" />
      <el-table-column label="表达式内容" align="center" prop="expression" />
      <el-table-column label="备注" align="center" prop="remark" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page-sizes="[5,10]"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { listExpression } from "@/api/flowable/expression";
import { StrUtil } from "@/utils/StrUtil";
const { proxy } = getCurrentInstance();
const { sys_common_status } = proxy.useDict('sys_common_status');

const queryForm = ref(null);
const loading = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const showSearch = ref(true);
const total = ref(0);
const expressionList = ref([]);
const title = ref("");
const open = ref(false);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  name: null,
  expression: null,
  status: null,
});
const radioSelected = ref(null);

const selectValues = defineProps(['selectValues']);

watch(selectValues, (newVal) => {
  if (StrUtil.isNotBlank(newVal)) {
    radioSelected.value = newVal;
  }
}, { immediate: true });

const getList = () => {
  loading.value = true;
  listExpression(queryParams.value).then(response => {
    expressionList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
};

const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

const resetQuery = () => {
  resetForm(queryForm.value);
  handleQuery();
};

const handleSingleExpSelect = (selection) => {
  radioSelected.value = selection.id; // 点击当前行时, radio 同样有选中效果
  emit('handleSingleExpSelect', selection);
};

const emit = defineEmits(['handleSingleExpSelect']);

onMounted(() => {
  getList();
});
</script>
