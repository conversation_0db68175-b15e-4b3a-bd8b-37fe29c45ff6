<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="流程名称" prop="procDefName">
        <el-input
          v-model="queryParams.procDefName"
          placeholder="请输入流程名称"
          clearable
          size="small"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="流程标题" prop="processTitle">
        <el-input
          v-model="queryParams.processTitle"
          placeholder="请输入流程标题"
          clearable
          size="small"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否完成" prop="markCompleted">
        <el-select v-model="queryParams.markCompleted" clearable placeholder="请选择">
          <el-option
            v-for="item in markCompletedOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="开始时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['flowable:deployment:add']"
        >新增流程</el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
        :columns="columns"
      />
    </el-row>

    <el-table v-loading="loading" :data="myProcessListData" border>
      <el-table-column label="流程编号" align="center" prop="procInsId" :show-overflow-tooltip="true" v-if="columns[0].visible"/>
      <el-table-column label="流程标题" align="center" prop="processTitle" :show-overflow-tooltip="true" v-if="columns[1].visible"/>
      <el-table-column label="流程名称" align="center" prop="procDefName" :show-overflow-tooltip="true" v-if="columns[2].visible"/>
      <el-table-column label="流程类别" align="center" prop="category" width="100px" v-if="columns[3].visible"/>
      <el-table-column label="流程版本" align="center" width="80px" v-if="columns[4].visible">
        <template v-slot:default="scope">
          <el-tag size="medium">v{{ scope.row.procDefVersion }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="提交时间" align="center" prop="createTime" width="180" v-if="columns[5].visible"/>
      <el-table-column label="流程状态" align="center" width="100" v-if="columns[6].visible">
        <template v-slot:default="scope">
          <el-tag v-if="scope.row.finishTime == null" size="mini">进行中</el-tag>
          <el-tag type="success" v-else size="mini">已完成</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="耗时" align="center" prop="duration" width="180" v-if="columns[7].visible"/>
      <el-table-column label="发起人" align="center" prop="startUserName" width="180" v-if="columns[8].visible"/>
      <el-table-column label="当前节点" align="center" prop="taskName" v-if="columns[9].visible"/>
      <el-table-column label="办理" align="center" v-if="columns[10].visible">
        <template v-slot:default="scope">
          <label v-if="scope.row.assigneeName">{{ scope.row.assigneeName }} 
            <el-tag type="info" size="mini" v-if="scope.row.deptName">{{ scope.row.deptName }}</el-tag>
          </label>
          <label v-else-if="scope.row.candidate">{{ scope.row.candidate }}</label>
          <label v-else-if="scope.row.taskName">{{ scope.row.taskName }}</label>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot:default="scope">
          <el-button size="mini" type="text" icon="el-icon-tickets" @click="handleFlowRecord(scope.row)" v-hasPermi="['flowable:deployment:list']">
            详情
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-circle-close" @click="handleStop(scope.row)" v-hasPermi="['flowable:task:stopProcess']" v-if="!scope.row.finishTime">
            取消申请
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['flowable:instance:del']">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 发起流程 -->
    <el-dialog :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form :model="queryProcessParams" ref="queryProcessForm" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="名称" prop="name">
          <el-input
            v-model="queryProcessParams.name"
            placeholder="请输入名称"
            clearable
            size="small"
            @keyup.enter="handleProcessQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleProcessQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetProcessQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="processLoading" fit :data="definitionList" border>
        <el-table-column label="流程名称" align="center" prop="name" />
        <el-table-column label="流程版本" align="center">
          <template v-slot:default="scope">
            <el-tag size="medium">v{{ scope.row.version }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="流程分类" align="center" prop="category" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template v-slot:default="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit-outline"
              @click="handleStartProcess(scope.row)"
            >发起流程</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="processTotal>0"
        :total="processTotal"
        :page.sync="queryProcessParams.pageNum"
        :limit.sync="queryProcessParams.pageSize"
        @pagination="listDefinition"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { myProcessList, stopProcess } from '@/api/flowable/process'
import { listDefinitionLast } from '@/api/flowable/definition'

const router = useRouter()

// 遮罩层
const loading = ref(false)
const processLoading = ref(true)
// 选中数组
const ids = ref([])
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
// 显示搜索条件
const showSearch = ref(true)
// 日期范围
const dateRange = ref([])
// 总条数
const total = ref(0)
const processTotal = ref(0)
// 我发起的流程列表数据
const myProcessListData = ref([])
// 弹出层标题
const title = ref("")
// 是否显示弹出层
const open = ref(false)
const src = ref("")
const definitionList = ref([])
// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  procDefName: null,
  processTitle: null,
  category: null,
  key: null,
  markCompleted: null
})
const markCompletedOptions = ref([
  { value: '1', label: '是' },
  { value: '0', label: '否' }
])
// 查询参数
const queryProcessParams = reactive({
  pageNum: 1,
  pageSize: 10,
  name: null,
  category: null,
  key: null
})
// 列信息
const columns = ref([
  { key: 0, label: `流程编号`, visible: false },
  { key: 1, label: `流程标题`, visible: true },
  { key: 2, label: `流程名称`, visible: true },
  { key: 3, label: `流程类别`, visible: true },
  { key: 4, label: `流程版本`, visible: true },
  { key: 5, label: `提交时间`, visible: true },
  { key: 6, label: `流程状态`, visible: true },
  { key: 7, label: `耗时`, visible: true },
  { key: 8, label: `发起人`, visible: true },
  { key: 9, label: `当前节点`, visible: true },
  { key: 10, label: `办理`, visible: true }
])

const timeDefault = () => {
  let date = new Date()
  // 通过时间戳计算
  let defalutStartTime = date.getTime() - 7 * 24 * 3600 * 1000 // 转化为时间戳 -几即表示几天内
  let defalutEndTime = date.getTime()
  let startDateNs = new Date(defalutStartTime)
  let endDateNs = new Date(defalutEndTime)
  // 月，日 不够10补0
  defalutStartTime =
    startDateNs.getFullYear() +
    "-" +
    (startDateNs.getMonth() + 1 >= 10
      ? startDateNs.getMonth() + 1
      : "0" + (startDateNs.getMonth() + 1)) +
    "-" +
    (startDateNs.getDate() >= 10
      ? startDateNs.getDate()
      : "0" + startDateNs.getDate()) + ' 00:00:00'
  defalutEndTime =
    endDateNs.getFullYear() +
    "-" +
    (endDateNs.getMonth() + 1 >= 10
      ? endDateNs.getMonth() + 1
      : "0" + (endDateNs.getMonth() + 1)) +
    "-" +
    (endDateNs.getDate() >= 10
      ? endDateNs.getDate()
      : "0" + endDateNs.getDate()) + ' 23:59:59'
  return [defalutStartTime, defalutEndTime]
}

const getList = () => {
  loading.value = true
  myProcessList(timeDefault()).then(response => {
    myProcessListData.value = response.data
    total.value = response.total
    loading.value = false
  })
}

const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

const resetQuery = () => {
  resetForm("queryForm")
  handleQuery()
}

const handleProcessQuery = () => {
  queryProcessParams.pageNum = 1
  listDefinition()
}

const resetProcessQuery = () => {
  resetForm("queryProcessForm")
  handleProcessQuery()
}

const handleAdd = () => {
  open.value = true
  title.value = "发起流程"
  listDefinition()
}

const listDefinition = () => {
  listDefinitionLast(queryProcessParams).then(response => {
    definitionList.value = response.rows
    processTotal.value = response.total
    processLoading.value = false
  })
}

const handleStartProcess = row => {
  open.value = false
  router.push({ path: '/flowable/task/record/index',
    query: {
      deployId: row.deploymentId,
      procDefId: row.id,
      formId: row.formid,
      finished: true
    }
  })
}

const handleStop = row => {
  const params = {
    instanceId: row.procInsId
  }
  stopProcess(params).then(res => {
    ElMessage.success(res.msg)
    getList()
  })
}

const handleFlowRecord = row => {
  router.push({ path: '/flowable/task/record/index',
    query: {
      procInsId: row.procInsId,
      deployId: row.deployId,
      taskId: row.taskId,
      formId: row.formId,
      finished: true,
      preview: true
    }
  })
}

const handleDelete = row => {
  const ids = row.procInsId
  ElMessageBox.confirm(`是否确认删除流程定义编号为"${ids}"的数据项?`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    return delDeployment(ids)
  }).then(() => {
    getList()
    ElMessage.success("删除成功")
  })
}

const resetForm = formName => {
  // 重置表单逻辑，这里假设 Vue2 的 resetForm 方法直接可用。
  // 如果需要完全重构，需要根据具体场景实现。
}

onMounted(() => {
  dateRange.value = timeDefault()
  getList()
})
</script>
