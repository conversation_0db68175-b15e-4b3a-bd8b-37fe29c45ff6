// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

.el-dropdown {
  color: #ffffff;
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse>div>.el-submenu>.el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

.el-dropdown .el-dropdown-link {
  color: var(--el-color-primary) !important;
}

.el-table__empty-text {
  font-size: 15px !important;
}

.el-input__wrapper {
  background: linear-gradient(180deg, rgb(2 136 243 / 36%) 0%, rgba(0, 130, 234, 0.1) 100%) !important;
  box-shadow: 0 0 0 1px #0452a6 inset !important;
}

:deep(.el-input__wrapper) {
  // background-color: #ffffff00;
  background: linear-gradient(180deg, rgb(2 136 243 / 36%) 0%, rgba(0, 130, 234, 0.1) 100%);
  border: 1px solid rgb(90, 174, 226);
  box-shadow: 0 0 0 0 #91afce inset;
  border-radius: 0px !important;
}

:deep(.el-input__inner) {
  color: #ffffff;
}



.el-message {
  top: 20px !important;
}

.el-message:not(:last-child) {
  visibility: hidden;
}



// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
    // background-color: #00c0ff !important;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse>div>.el-submenu>.el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

// .el-table tr {
//   background-color: #ffffff00;
// }

.el-table {
  background-color: rgba(0, 103, 214, 0);
  color: #ffffff;
  border: none;

  .el-table__header-wrapper {
    background-color: rgba(0, 103, 214, 0.1);

    th {
      // background-color: rgba(0, 103, 214, 0.1) !important;
      color: #ffffff;
      font-weight: bold;
      border-bottom: none;
    }
  }

  .el-table__body-wrapper {
    max-height: 65vh;
    overflow-y: auto;

    td {
      // background-color: rgba(0, 103, 214, 0.1) !important;
      color: #ffffff;
      border-bottom: 1px solid #1f365e;
    }

    tr:hover>td {
      background-color: rgba(0, 103, 214, 0.1) !important;
    }
  }

  .el-table__row {
    transition: background-color 0.3s;
  }

  .cell {
    padding: 12px;
  }
}

.el-table .el-table__header-wrapper {
  background-color: rgba(36, 148, 255, 0.5) !important;
}

.el-table .el-table__header-wrapper th {
  color: #ffffff !important;
  background: rgba(36, 148, 255, 0.2) !important;
  background-color: rgba(36, 148, 255, 0.2) !important;
}

.el-table .el-table__fixed-header-wrapper th {
  color: #ffffff !important;
  background: rgba(36, 148, 255, 1) !important;
  background-color: rgba(36, 148, 255, 1) !important;
}

.el-table__cell .fixed-width {
  background-color: #08366c !important;
}

.el-table tr {
  // background-color: #0a2245 !important;
  // background-color: #0f3b6c !important;
  // background-color: #0e4e99 !important;
  // background-color: #0e4e9900 !important;
  background-color: #0e4e9952 !important;
  // background-color: rgba(36, 148, 255, 1) !important;
}

.el-table .el-table__cell.is-center {
  // background-color: #092b57 !important;
}

/* 修改斑马纹背景色（奇数行或偶数行） */
.el-table--striped .el-table__body tr.el-table__row--striped td {
  // background-color: #064690 !important;
  background-color: #2494ff17 !important;
}

.el-table__row .el-table__row--striped {
  // background-color: #064690 !important;
  background-color: #2494ff17 !important;
}

.el-table__fixed .el-table__body tr:hover>td,
.el-table__fixed-right .el-table__body tr:hover>td {
  background-color: #2367ce77 !important;
  /* 与主表统一 */
  color: #e7e0e0 !important;
}

.el-table__body tr.hover-row>td.el-table__cell {
  background-color: #2367ce3d !important;
  /* 与主表统一 */
}

.el-table__fixedight .el-table body tr:hover>td {
  background-color: #2367ce3d !important;
  /* 与主表统一 */
}

/* 全局覆盖 */
.el-table__body tr:hover>td,
.el-table__fixed .el-table__body tr:hover>td,
.el-table__fixed-right .el-table__body tr:hover>td {
  background-color: #2367ce18 !important;
  /* 与主表统一 */
  color: #fcfeff !important;
}

.el-table th.el-table__cell.is-leaf,
.el-table td.el-table__cell {
  border-bottom: 1px solid #dfe6ec00 !important;
}

// .el-table th.el-table__cell.is-leaf,
// .el-table td.el-table__cell {
//   // border-bottom: 1px solid #06468c;
//   // background-color: #2664b5 !important;
//   border-bottom: 1px solid #082754;
//   background-color: #082a57 !important;
// }

.el-table--medium .el-table__cell {
  padding: 0px !important;
}

.el-table .el-table__cell {
  padding: 0px !important;
}

.el-pagination.is-background .btn-prev,
.el-pagination.is-background .btn-next,
.el-pagination.is-background .el-pager li {
  background-color: rgba(58, 166, 255, 0.1);
}

.el-pagination.is-background .el-pager li {
  background-color: rgba(58, 166, 255, 0.1);
  color: #606266;
}

.el-pagination__total {
  color: #ffffff;
}

.el-pagination__jump {
  color: #ffffff;
}

.el-pagination {
  background-color: transparent;
  color: #ffffff;
  justify-content: center;
  // margin-top: 16px;

  .el-pager li {
    background-color: transparent;
    color: #ffffff;
    border-radius: 4px;
    margin: 0 4px;
    padding: 1px 10px;
    transition: background-color 0.3s;

    &.active {
      background-color: #1f5eff;
      color: #ffffff;
      font-weight: bold;
    }

    &:hover {
      background-color: #2c4a82;
    }
  }

  button {
    color: #ffffff;

    &:hover {
      color: #409EFF;
    }

    &.disabled {
      color: #555;
    }
  }
}

.el-menu {
  background-color: #30415600;
}

.el-menu-item.is-active {
  // background-color: #14294f00 !important;
  background: linear-gradient(270deg, rgba(0, 56, 124, 0) 0%, rgba(36, 148, 255, 0.7) 100%) !important;
  // background-image: url(~@/assets/safetyAlerts/<EMAIL>) !important;
  // background-size: 100%;
  // background-repeat: no-repeat;
  // width: 140px;
  color: #ffffff !important;
}

// .el-menu-item {
//   width: 140px;
//   text-align: center;
//   height: 30px;
//   line-height: 26px;
// }

// .el-submenu__title {
//   height: 30px;
//   line-height: 26px;
// }

.el-table th {
  height: 48px; // 设置表头高度
  line-height: 48px;
  padding: 0;
}

.el-table::before {
  background-color: #e6ebf500;
}

.el-table .el-table__body td {
  height: 44px; // 设置行高
  line-height: 44px;
  padding: 0;
}

.el-table--border .el-table__cell {
  border-right: 1px solid #dfe6ec00;
}

.el-table::before,
.el-table--group::after,
.el-table--border::after {
  background-color: #1c2e5300;
}

// .el-menu--horizontal {
//   width: 140px !important;
// }
#app .sidebar-container .theme-dark .is-active>.el-submenu__title {
  // color: #ffffff !important;
  // background-color: #14294f00 !important;
  background: linear-gradient(90deg, rgba(0, 72, 180, 0) 0%, rgb(39 95 149 / 58%) 100%) !important;
  // background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIwAAAAcCAYAAACzpld9AAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAALsSURBVGiB7ZtPSBRxFMe/v9/M7K75b3Mt0IgijDCj6GR/Lx2yQxAeqksURRBEdCpIKtoOQQcRKjpEN6EQOoinMiTKIPMQQSZBKZFSIeoubpvrrDu/10GzTXfdma1tZ+19Lo/3fu/NfGGG95uZ3/wEEJRAvwDDZKSOBACB7S2+fEthCoCeiKkDIACAN+LNrxrG1ZhlJhBUs1NRUGJvcWV+FTGuJTZNeN40BgjSZyJBhYmbU6hAWX6VMa6kxBsBBAGAPhfsDUV9+6v8ZJlG3oQxrkNYQk09Ho/O+b+NHrxT7pmU1fAAiANs2cYnxQg6T4Ywy4LXaU9ja61BFj8AM0gIOWm2H3ufHNPnJ3kT+mjCR+v+nSzGrZhmYnB+LOUHu5LDbbVCykDuJTFuRUlMfL93qG9+fEGHAYBoUexjiVVelXtZjFsxvonhVPG0SwLLj3ZstiBW5U4S41Y00Odw64E3qcZSdhgACIflgD+g8bPMf4aCiIc9Q+/SjS+66FhxvGujpdMmLSlmAWB/6fpxaP3Ru3v6kYa0HQYAQtHQgN8f2EDSKP4Zk7BASadgf+n4QlA8Oj7yAYuQ8beGFaeerbd0Y3emPGYpoHpDt3el7S6AjRsGAAJnXh6BkKV/RxTjRqSksdEb9Q8y5S06Jf3KMl7pQAOQ/zmW/dz4CaG9gA1s/2m38lxfo5SosZvPFA6k1PBI85Y2O7n2OgyAaW3ZEx+m67KXxbgVQarHdq6TA1ddHGgUQm51LolxK0Tq9ddrNe128213GAAwLOOp8mKbc1mMKxGI6RAPnZU4ZPXVLw0Q2Oe0jnEhhEfDV6o7nZQ46jAAQKVWtxbz7QQoAAAKgEwaZ78wfAURo6Kpbjgkq/1Ia66P7wDkiWxqGXdAZLUNNVV2Oa3LegPb2uZIi4DknQYFiCIa+nS+9FI2tY6npDmk0QGB0yDM3HZsC8aSkvdTX9TM/NEW2Zpb1gUiqoeQACmwLQRLbwfPapezveY/AJXaNuMRZpbUAAAAAElFTkSuQmCC) !important;
  // background-size: 100%;
  // background-repeat: no-repeat;
  // height: 28px;
  // width: 140px;
  // text-align: center;
  // margin-left: -20px;
}

.el-menu-item:hover {
  color: rgb(11, 184, 89) !important;
  // background-color: rgba(28, 98, 163, 0.199) !important;
  background: linear-gradient(270deg, rgba(0, 56, 124, 0) 0%, rgba(36, 148, 255, 0.7) 100%) !important;
}

// .el-menu--horizontal .el-menu .el-menu-item, .el-menu--horizontal .el-menu .el-submenu__title{
//   height: 35px !important;
//   line-height: 28px !important;
// }
// .el-menu--popup-bottom-start {
//   margin-left: -20px !important;
//   width: 140px !important;
// }

// .el-menu--popup {
//   min-width: 140px;
//   background-color: rgb(0 21 41 / 63%) !important;
// }

.el-form-item__label {
  color: #ffffff;
  font-weight: 300 !important;
}

.el-input__inner {
  color: #ffffff;
  background: #ffffff00 !important;
  // border: 1px solid #636e87;
  // background: linear-gradient(180deg, rgb(2 136 243 / 36%) 0%, rgba(0, 130, 234, 0.1) 100%);
  // color: #ffffff;
  // border: 1px solid #29478f;
  // // background-color: #0a376c;
  // background: linear-gradient(180deg, rgba(0, 130, 234, 0.5) 0%, rgba(0, 130, 234, 0.1) 100%);
}

.el-range-editor--small .el-range-input {
  background: #075094;
}

.el-button {
  // background: #09376c;
  background: linear-gradient(180deg, rgba(0, 72, 159, 0.26) 0%, rgba(0, 103, 214, 0.6) 100%);
  border: 1px solid #09376c;
  border-color: #4e7ff1;
  color: #ffffff;
}

.el-button--primary {
  color: #ffffff;
  background: linear-gradient(180deg, rgba(0, 72, 159, 0.26) 0%, rgba(0, 103, 214, 0.6) 100%);
  border-color: #3e92dd;
}

.el-button--primary.is-plain {
  color: #ffffff;
  // background: #367FC3;
  background: linear-gradient(180deg, rgba(0, 72, 159, 0.26) 0%, rgba(0, 103, 214, 0.6) 100%);
  border-color: #549ad9;
}

.el-button--warning.is-plain {
  color: #ffffff;
  background: rgba(250, 247, 88, 0.2);
  border-color: rgba(250, 247, 88, 0.2);
}

.el-loading-mask {
  background-color: rgb(9 58 112 / 47%);
}

.v-modal {
  opacity: 1;
  background: rgba(0, 0, 0, 0.5);
}

.el-dialog {
  background: rgba(9, 53, 102, 1);
}

.el-dialog__header {
  padding-bottom: 0px !important;
  padding: 15px !important;
  background: linear-gradient(270deg, rgba(0, 72, 141, 0.3) 0%, rgba(36, 148, 255, 0.5) 100%);
}

.el-dialog__title {
  color: #ffffff;
}

.el-dialog:not(.is-fullscreen) {
  margin-top: 10vh !important;
}

.el-dialog .el-dialog__body {
  max-height: 622px !important;
  overflow-y: auto;
}

.el-dialog__body {
  color: #ffffff;
}

.el-tree {
  background: #ffffff00;
  color: #ffffff;
}

.el-card {
  border: 1px solid #2869e9;
  background-color: #ffffff00;
  overflow: hidden;
  color: #ffffff;
}

.el-tabs__item {
  color: #ffffff;
}

.el-tabs__nav-wrap::after {
  background-color: #4fb9f9;
}

.el-textarea__inner {
  background-color: #074583;
  border: 1px solid #3c5795;
}

.vue-treeselect__control {
  color: #ffffff !important;
  border: 1px solid #29478f !important;
  background: linear-gradient(180deg, rgba(0, 130, 234, 0.5) 0%, rgba(0, 130, 234, 0.1) 100%) !important;
}

.vue-treeselect__single-value {
  color: #ffffff !important;
}


.link-type,
.link-type:focus {
  color: #0eff4f;
}

.el-input.is-disabled .el-input__inner {
  background-color: #074989;
}

.el-radio {
  color: #ffffff;
}

.el-message-box {
  background-color: #132a35;
  border-radius: 10px;
  border: 1px solid #25417b;
}

.el-message-box__title {
  color: #fcfdff;
}

.el-message-box__content {
  color: #ffba00;
}

.el-divider--vertical {
  height: 2em;
  margin: 0 4px;
}

.el-select {
  width: 100% !important;
}

.el-input-number--medium {
  width: 100%;
}

.el-button--info.is-plain {
  color: #ffffff;
  background: #297bc7;
  border-color: #2c9add;
}

.el-upload__tip {
  color: #ffffff;
}

.el-form-item.el-form-item--mini+.el-form-item {
  margin-top: 0px !important;
}

.el-table__expand-icon {
  color: #3aff94;
  font-size: 14px;
}

.el-input-number__increase,
.el-input-number__decrease {
  background: #485876;
  color: #ffffff;
}

.el-dropdown-menu {
  border: solid 1px #054a98;
  background-color: #082957;
}

.el-dropdown-menu__item {
  color: #d5d5d5;
}

.el-dropdown-menu__item--divided:before {
  background-color: #000000;

}

.el-dropdown-menu__item:hover {
  background-color: #082957 !important;
}

.el-select-dropdown {
  border: solid 1px #054a98;
  background-color: #082957;
}

.el-select-dropdown__item {
  color: #d5d5d5;
}

.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: #2260a58c;
}



/* 全局样式 - el-tree 美化（蓝色科技风） */
.el-tree {
  background-color: transparent !important;
  color: #d0e8ff !important;
  /* 字体变亮蓝 */
  font-size: 14px;
}

.el-tree-node__content {
  height: 38px;
  border-radius: 6px;
  margin: 4px 6px;
  padding-left: 8px;
  transition: all 0.3s;
}

.el-tree-node__content:hover {
  background-color: rgba(0, 192, 255, 0.2);
  /* hover 蓝色透明 */
  color: #ffffff !important;
}

.el-tree-node.is-current>.el-tree-node__content {
  background-color: rgba(0, 192, 255, 0.4);
  /* 当前项高亮 */
  border-left: 3px solid #00c0ff;
  color: #ffffff !important;
}

// .el-tree-node__expand-icon {
//   color: #9acfff !important;
// }
/* 仅当不是叶子节点时，设置展开图标颜色 */
.el-tree-node:not(.is-leaf)>.el-tree-node__content>.el-tree-node__expand-icon {
  // color: #77ff89 !important;
}

.el-tree-node.is-leaf>.el-tree-node__content>.el-tree-node__expand-icon {
  display: none;
}

.el-tree-node__label {
  color: inherit !important;
}

.el-tree-node__content {
  box-shadow: 0 1px 3px rgba(0, 192, 255, 0.15);
  border-radius: 6px;
}

.el-tree>.el-tree-node {
  margin-bottom: 6px;
}

.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  background-color: #3b89d7 !important;
}

.el-date-editor .el-range-input {
  color: #ffffff;
  background-color: #0457aa00;
}

.el-date-editor .el-range-separator {
  color: #ffffff;
}

.el-descriptions {
  color: #ffffff !important;
}

.el-descriptions__body {
  background-color: #ffffff00 !important;
  color: #ffffff !important;
}

.el-descriptions-item__label.is-bordered-label {
  color: #ffffff;
  background: rgba(36, 148, 255, 0.4) !important;
}

.el-tree-select-popper {
  background: rgb(0, 52, 100) !important;
}

.el-descriptions .is-bordered .el-descriptions-item__cell {
  border: 1px solid rgba(195, 226, 255, 0.2);
  height: 70px !important;
}

.el-textarea__inner {
  color: #ffffff !important;
}

.el-input.is-disabled .el-input__inner {
  color: #ffffff !important;
}

.el-upload-dragger {
  background-color: #0b4682;
  border: 1px dashed #161010;
}

.el-upload-dragger .el-upload__text {
  color: #ffffff;
}

.el-link.el-link--primary {
  color: #67e9a2;
}

.el-link.el-link--default {
  color: #5efff9;
}

.el-picker-panel {
  color: #ffffff;
  border: 1px solid #3463b7;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background: #093566;
}

.el-picker-panel__footer {
  border-top: 1px solid #093566;
  background-color: #093566;
}

.el-tree-node__content {
  &:hover {
    background-color: #3b89d7 !important;
  }

  &:active {
    background-color: #3b89d7 !important;
  }

  &:focus {
    background-color: #3b89d7 !important;
  }
}

/* 统一选中节点背景色 */
.el-tree .el-tree-node__content {
  transition: background-color 0.2s;
}

/* 当前选中节点 */
.el-tree .el-tree-node.is-current>.el-tree-node__content {
  background-color: #3b89d7;
  /* 你想要的颜色 */
}

/* 鼠标 hover 时也有背景 */
.el-tree .el-tree-node__content:hover {
  background-color: #3b89d7;
}

/* el-tree主题样式复写之后通过小三角展开节点并离开后背景色变白色问题 !!!!!!!!!!*/
.el-tree .el-tree-node:focus>.el-tree-node__content {
  background-color: #3b89d7;
}


.el-upload-list__item-name {
  color: #ffffff;
}

.el-cascader__dropdown {
  background: #093566 !important;
  border: solid 1px #084a8a !important;
}

.el-cascader-menu {
  color: #ffffff !important;
}

.el-cascader-node:not(.is-disabled):hover,
.el-cascader-node:not(.is-disabled):focus {
  background: #336297 !important;
}

.el-picker-panel__icon-btn {
  color: #ffffff !important;
}

.el-date-picker__header-label {
  color: #ffffff !important;
}

.el-date-table th {
  color: #ffffff !important;
}


.el-range-editor.el-input__inner {
  width: 100%;
}

.el-table__fixed::before,
.el-table__fixed-right::before {
  height: 0px;
  background-color: #ffffff00;
}

.el-table__inner-wrapper::before {
  background-color: #0b193800;
}

.el-button.is-link {
  color: #a0edca;
}

.el-tree {
  background-color: #08407b !important;
  color: #ffffff !important;
}

.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
  background-color: #08407b;
}

.el-descriptions__label.el-descriptions__cell.is-bordered-label {
  color: #ffffff;
  background: #f5f7fa00;
}

.el-descriptions__content.el-descriptions__cell.is-bordered-content {
  color: #ffffff;
}