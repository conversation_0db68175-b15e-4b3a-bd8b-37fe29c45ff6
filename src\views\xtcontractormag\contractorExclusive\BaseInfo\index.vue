<template>
  <div class="contractor-intro-container">

    <!-- 承包商详细信息 -->
    <div v-if="contractorInfo" class="contractor-details">
      <!-- 基本信息卡片 -->
      <el-card shadow="hover" class="info-card basic-info-card">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><InfoFilled /></el-icon>
            <span class="header-title">基本信息</span>
            <el-tag
              :type="getStatusType(contractorInfo.status)"
              size="large"
              class="status-tag"
            >
              {{ getStatusText(contractorInfo.status) }}
            </el-tag>
          </div>
        </template>

        <div class="basic-info-content">
          <div class="info-row">
            <div class="info-item">
              <div class="info-label">
                <el-icon><OfficeBuilding /></el-icon>
                承包商名称
              </div>
              <div class="info-value company-name">{{ contractorInfo.contractorName }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">
                <el-icon><Collection /></el-icon>
                承包商类型
              </div>
              <div class="info-value">
                <el-tag type="info">{{ getContractorTypeText(contractorInfo.contractorType) }}</el-tag>
              </div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <div class="info-label">
                <el-icon><User /></el-icon>
                主要负责人
              </div>
              <div class="info-value">{{ contractorInfo.principal }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">
                <el-icon><Phone /></el-icon>
                联系方式
              </div>
              <div class="info-value">
                <a :href="'tel:' + contractorInfo.managerPhone" class="phone-link">
                  {{ contractorInfo.managerPhone }}
                </a>
              </div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <div class="info-label">
                <el-icon><Document /></el-icon>
                信用代码
              </div>
              <div class="info-value credit-code">{{ contractorInfo.creditCode }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">
                <el-icon><Files /></el-icon>
                营业执照
              </div>
              <div class="info-value">
                <el-button
                  v-if="contractorInfo.businessLicenseUrl"
                  type="primary"
                  link
                  @click="previewLicense"
                >
                  <el-icon><View /></el-icon>
                  查看执照
                </el-button>
                <span v-else class="no-data">暂无</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 资质信息卡片 -->
      <el-card shadow="hover" class="info-card qualification-card">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><Medal /></el-icon>
            <span class="header-title">资质信息</span>
            <el-tag type="success" size="large">
              共 {{ qualificationList.length }} 项资质
            </el-tag>
          </div>
        </template>

        <div class="qualification-content">
          <div v-if="qualificationList.length === 0" class="empty-state">
            <el-empty description="暂无资质信息" />
          </div>

          <div v-else class="qualification-list">
            <div
              v-for="qualification in qualificationList"
              :key="qualification.id"
              class="qualification-item"
            >
              <div class="qualification-header">
                <div class="qualification-title">
                  <el-icon><Star /></el-icon>
                  <span class="title-text">{{ qualification.qualificationName }}</span>
                  <el-tag
                    :type="getQualificationLevelType(qualification.qualificationLevel)"
                    size="small"
                  >
                    {{ qualification.qualificationLevel }}
                  </el-tag>
                </div>
                <div class="qualification-type">
                  <el-tag type="info" size="small">{{ qualification.type }}</el-tag>
                </div>
              </div>

              <div class="qualification-details">
                <div class="detail-row">
                  <div class="detail-item">
                    <span class="detail-label">证书编号：</span>
                    <span class="detail-value">{{ qualification.certificateNumber }}</span>
                  </div>
                </div>

                <div class="detail-row">
                  <div class="detail-item">
                    <span class="detail-label">有效期：</span>
                    <span class="detail-value validity-period">
                      {{ formatDate(qualification.certificateValidityStart) }}
                      至
                      {{ formatDate(qualification.certificateValidityEnd) }}
                      <el-tag
                        :type="getValidityStatus(qualification.certificateValidityEnd).type"
                        size="small"
                        class="validity-tag"
                      >
                        {{ getValidityStatus(qualification.certificateValidityEnd).text }}
                      </el-tag>
                    </span>
                  </div>
                </div>

                <div v-if="qualification.attachmentUrl" class="detail-row">
                  <div class="detail-item">
                    <span class="detail-label">证书附件：</span>
                    <el-button
                      type="primary"
                      link
                      size="small"
                      @click="previewAttachment(qualification.attachmentUrl)"
                    >
                      <el-icon><Paperclip /></el-icon>
                      查看附件
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!loading" class="empty-contractor">
      <el-empty description="请选择要查看的承包商" />
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="图片预览"
      width="60%"
      center
    >
      <div class="preview-container">
        <iframe style="width: 100%;height: 100%;min-height: 50vh;" :src="previewImageUrl" alt="预览图片" class="preview-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

import { listBasic, getBasic } from '@/api/xtcontractormag/BaseInfo/basic'
import { listQualification } from '@/api/xtcontractormag/BaseInfo/qualification'
import { useDict } from '@/utils/dict'

// 字典数据
const { cbs_contractor_type } = useDict('cbs_contractor_type')

// 响应式数据
const loading = ref(false)
const contractorList = ref([])
const contractorInfo = ref(null)
const qualificationList = ref([])
const previewVisible = ref(false)
const previewImageUrl = ref('')

// 获取承包商列表并自动加载第一条
const getContractorList = async () => {
  try {
    loading.value = true
    const response = await listBasic({})
    contractorList.value = response.rows || []

    // 自动加载第一条数据
    if (contractorList.value.length > 0) {
      const firstContractor = contractorList.value[0]
      await getContractorDetail(firstContractor.id)
      await getQualificationList(firstContractor.id)
    }
  } catch (error) {
    ElMessage.error('获取承包商列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 获取承包商详细信息
const getContractorDetail = async (id) => {
  try {
    loading.value = true
    const response = await getBasic(id)
    contractorInfo.value = response.data
  } catch (error) {
    ElMessage.error('获取承包商详细信息失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 获取承包商资质列表
const getQualificationList = async (basicId) => {
  try {
    const response = await listQualification()
    qualificationList.value = response.rows || []
  } catch (error) {
    ElMessage.error('获取资质信息失败')
    console.error(error)
  }
}



// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    '1': 'success',
    '0': 'danger',
    'active': 'success',
    'inactive': 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    '1': '正常',
    '0': '停用',
    'active': '正常',
    'inactive': '停用'
  }
  return statusMap[status] || '未知'
}

// 获取承包商类型文本
const getContractorTypeText = (type) => {
  const typeItem = cbs_contractor_type.value.find(item => item.value === type)
  return typeItem ? typeItem.label : type
}

// 获取资质等级类型
const getQualificationLevelType = (level) => {
  const levelMap = {
    '特级': 'danger',
    '一级': 'warning',
    '二级': 'primary',
    '三级': 'success',
    '四级': 'info'
  }
  return levelMap[level] || 'info'
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未设置'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 获取有效期状态
const getValidityStatus = (endDate) => {
  if (!endDate) return { type: 'info', text: '未设置' }

  const now = new Date()
  const end = new Date(endDate)
  const diffTime = end - now
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays < 0) {
    return { type: 'danger', text: '已过期' }
  } else if (diffDays <= 30) {
    return { type: 'warning', text: '即将过期' }
  } else {
    return { type: 'success', text: '有效' }
  }
}

// 预览营业执照
const previewLicense = () => {
  if (contractorInfo.value?.businessLicenseUrl) {
    previewImageUrl.value = contractorInfo.value.businessLicenseUrl
    previewVisible.value = true
  }
}

// 预览附件
const previewAttachment = (url) => {
  if (url) {
    previewImageUrl.value = url
    previewVisible.value = true
  }
}

// 组件挂载时获取数据
onMounted(() => {
  getContractorList()
})
</script>

<style scoped>
.contractor-intro-container {
  padding: 24px;
  background: transparent;
  min-height: 100vh;
  position: relative;
}

/* 页面标题样式 */
.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 30px 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 16px;
  color: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
:deep(.el-card__header) {
  background: transparent !important;
  border-bottom: none !important;
  padding: 20px 20px 0 20px !important;
}

:deep(.el-card__body) {
  background: transparent !important;
  padding: 20px !important;
}

.page-title {
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 10px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.page-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

/* 选择器样式 */
.contractor-selector {
  margin-bottom: 32px;
  position: relative;
  z-index: 2;
}

.selector-card {
  border-radius: 16px;
  border: none;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.selector-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24px;
  padding: 24px 0;
}

/* 信息卡片样式 */
.contractor-details {
  display: flex;
  flex-direction: column;
  gap: 24px;
  position: relative;
  z-index: 2;
}

.info-card {
  border-radius: 16px;
  border: none;
  background: transparent;
  box-shadow: none;
  transition: all 0.3s ease;
}

.info-card:hover {
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
  font-size: 18px;
  color: #ffffff;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-icon {
  margin-right: 8px;
  font-size: 20px;
  color: #64b5f6;
}

.header-title {
  flex: 1;
  display: flex;
  align-items: center;
  color: #ffffff;
}

.status-tag {
  font-weight: 500;
}

/* 基本信息样式 */
.basic-info-content {
  padding: 10px 0;
}

.info-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 24px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #ffffff;
  font-size: 14px;
}

.info-value {
  font-size: 16px;
  color: #64b5f6;
  font-weight: 500;
}

.company-name {
  font-size: 20px;
  font-weight: 600;
  color: #81c784;
}

.credit-code {
  font-family: 'Courier New', monospace;
  background: rgba(100, 181, 246, 0.1);
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid rgba(100, 181, 246, 0.3);
  color: #64b5f6;
}

.phone-link {
  color: #64b5f6;
  text-decoration: none;
  transition: color 0.3s ease;
}

.phone-link:hover {
  color: #90caf9;
  text-decoration: underline;
}

.no-data {
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
}

/* 资质信息样式 */
.qualification-content {
  padding: 10px 0;
}

.qualification-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.qualification-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.qualification-item:hover {
  background: rgba(100, 181, 246, 0.1);
  border-color: rgba(100, 181, 246, 0.3);
}

.qualification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.qualification-title {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.title-text {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
}

.qualification-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-row {
  display: flex;
  align-items: center;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-label {
  font-weight: 500;
  color: #ffffff;
  min-width: 80px;
}

.detail-value {
  color: #64b5f6;
}

.validity-period {
  display: flex;
  align-items: center;
  gap: 8px;
}

.validity-tag {
  margin-left: 8px;
}

/* 空状态样式 */
.empty-contractor {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background: transparent;
  border-radius: 16px;
  color: #ffffff;
}

/* 预览对话框样式 */


.preview-image {
  max-width: 100%;
  max-height: 70vh;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .contractor-intro-container {
    padding: 15px;
  }

  .page-title {
    font-size: 24px;
  }

  .selector-content {
    flex-direction: column;
    gap: 15px;
  }

  .info-row {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .qualification-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .detail-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* 动画效果 */
.contractor-details {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片进入动画 */
.info-card {
  animation: slideInLeft 0.5s ease-out;
}

.info-card:nth-child(2) {
  animation-delay: 0.1s;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>