/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2025-06-26 13:59:26
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2025-06-30 19:58:07
 * @FilePath: \park_enterprise_front\src\api\xtcontractormag\BaseInfo\basic.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

// 查询承包商基本信息列表
export function listBasic(query) {
  return request({
    url: '/contractor/basic',
    method: 'get',
    params: query
  })
}

// 查询承包商基本信息详细
export function getBasic(id) {
  return request({
    url: '/contractor/basic/' + id,
    method: 'get'
  })
}

// 新增承包商基本信息
export function addBasic(data) {
  return request({
    url: '/contractor/basic',
    method: 'post',
    data: data
  })
}

// 修改承包商基本信息
export function updateBasic(data) {
  return request({
    url: '/contractor/basic',
    method: 'post',
    data: data
  })
}

// 删除承包商基本信息
export function delBasic(data) {
  return request({
    url: '/contractor/basic/',
    method: 'put',
    data: data
  })
}
