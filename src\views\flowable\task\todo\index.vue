<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="流程名称" prop="procDefName">
        <el-input
          v-model="queryParams.procDefName"
          placeholder="请输入完整流程名称"
          clearable
          size="small"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="接收时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="batchhandle"
          v-hasPermi="['flowable:task:batchhandle']"
        >批量审批</el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
        :columns="columns"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="todoListData" @selection-change="handleSelectionChange" border>
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="任务编号" align="center" prop="taskId" :show-overflow-tooltip="true" v-if="columns[0].visible"/>
      <el-table-column label="流程标题" align="center" prop="processTitle" :show-overflow-tooltip="true" v-if="columns[1].visible"/>
      <el-table-column label="流程名称" align="center" prop="procDefName" v-if="columns[2].visible"/>
      <el-table-column label="任务节点" align="center" prop="taskName" v-if="columns[3].visible"/>
      <el-table-column label="办理" align="center" v-if="columns[4].visible">
        <template #default="scope">
          <label v-if="scope.row.assigneeName">{{scope.row.assigneeName}} <el-tag type="info" size="mini" v-if="scope.row.deptName">{{scope.row.deptName}}</el-tag></label>
          <label v-if="scope.row.candidate">{{scope.row.candidate}}</label>
          <label v-if="scope.row.taskName && scope.row.assigneeName===null && scope.row.candidate===null">{{scope.row.taskName}}</label>
          <label v-if="scope.row.taskName && scope.row.assigneeName===undefined && scope.row.candidate===undefined">{{scope.row.taskName}}</label>
        </template>
      </el-table-column>
      <el-table-column label="流程版本" align="center" v-if="columns[5].visible">
        <template #default="scope">
          <el-tag size="medium" >v{{scope.row.procDefVersion}}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="流程发起人" align="center" v-if="columns[6].visible">
        <template #default="scope">
          <label>{{scope.row.startUserName}} <el-tag type="info" size="mini" v-if="scope.row.startDeptName">{{scope.row.startDeptName}}</el-tag></label>
        </template>
      </el-table-column>
      <el-table-column label="接收时间" align="center" prop="createTime" width="180" v-if="columns[7].visible"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit-outline"
            @click="handleProcess(scope.row)"
          >处理
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { todoList, batchComplete } from "@/api/flowable/todo";

const router = useRouter()

// 选中数组
const ids = ref([])
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
// 遮罩层
const loading = ref(false)
// 显示搜索条件
const showSearch = ref(true)
// 总条数
const total = ref(0)
// 流程待办任务表格数据
const todoListData = ref([])
// 日期范围
const dateRange = ref([])
// 查询表单引用
const queryForm = ref(null)
// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  procDefName: null,
  category: null
})
// 列信息
const columns = ref([
  { key: 0, label: '任务编号', visible: false },
  { key: 1, label: '流程标题', visible: true },
  { key: 2, label: '流程名称', visible: true },
  { key: 3, label: '任务节点', visible: true },
  { key: 4, label: '办理', visible: true },
  { key: 5, label: '流程版本', visible: true },
  { key: 6, label: '流程发起人', visible: true },
  { key: 7, label: '接收时间', visible: true }
])

// 默认时间
const timeDefault = () => {
  let date = new Date();
  // 通过时间戳计算
  let defalutStartTime = date.getTime() - 7 * 24 * 3600 * 1000; // 转化为时间戳 -几即表示几天内
  let defalutEndTime = date.getTime();
  let startDateNs = new Date(defalutStartTime);
  let endDateNs = new Date(defalutEndTime);
  // 月，日 不够10补0
  defalutStartTime =
    startDateNs.getFullYear() +
    "-" +
    (startDateNs.getMonth() + 1 >= 10
      ? startDateNs.getMonth() + 1
      : "0" + (startDateNs.getMonth() + 1)) +
    "-" +
    (startDateNs.getDate() >= 10
      ? startDateNs.getDate()
      : "0" + startDateNs.getDate()) + ' 00:00:00';
  defalutEndTime =
    endDateNs.getFullYear() +
    "-" +
    (endDateNs.getMonth() + 1 >= 10
      ? endDateNs.getMonth() + 1
      : "0" + (endDateNs.getMonth() + 1)) +
    "-" +
    (endDateNs.getDate() >= 10
      ? endDateNs.getDate()
      : "0" + endDateNs.getDate()) + ' 23:59:59';
  return [defalutStartTime, defalutEndTime];
}

/** 查询流程定义列表 */
const getList = () => {
  loading.value = true;
  todoList({...queryParams, ...dateRange.value}).then(resp => {
    todoListData.value = resp.data;
    total.value = resp.total;
    loading.value = false;
  });
}

// 跳转到处理页面
const handleProcess = (row) => {
  router.push({ 
    path: '/flowable/task/record/index',
    query: {
      procInsId: row.procInsId,
      executionId: row.executionId,
      deployId: row.deployId,
      taskId: row.taskId,
      formId: row.formId,
      finished: false
    }
  })
}

/** 批量审批操作 */
const batchhandle = () => {
  if(ids.value.length > 10){
    ElMessage.warning('批量审批最多不能超过10条');
    return false;
  }
  batchComplete(ids.value).then(res => {
    if(res.code == 200){
      ElMessage.success(res.msg);
      getList();
    }else{
      ElMessage.error(res.msg);
    }
  })
}

// 多选框选中数据
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.taskId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryForm.value.resetFields();
  handleQuery();
}

// 初始化
dateRange.value = timeDefault();
getList();
</script>
