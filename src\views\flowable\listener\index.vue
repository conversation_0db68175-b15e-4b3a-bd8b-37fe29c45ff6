<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="监听类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择监听类型" clearable>
          <el-option
            v-for="dict in sys_listener_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['flowable:listener:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['flowable:listener:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['flowable:listener:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['flowable:listener:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="listenerList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="名称" align="center" prop="name" />
      <el-table-column label="监听类型" align="center" prop="type">
        <template v-slot:default="scope">
          <dict-tag :options="sys_listener_type" :value="scope.row.type"/>
        </template>
      </el-table-column>
      <el-table-column label="事件类型" align="center" prop="eventType"/>
      <el-table-column label="值类型" align="center" prop="valueType">
        <template v-slot:default="scope">
          <dict-tag :options="sys_listener_value_type" :value="scope.row.valueType"/>
        </template>
      </el-table-column>
      <el-table-column label="执行内容" align="center" prop="value" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot:default="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['flowable:listener:edit']"
          >编辑</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['flowable:listener:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改流程监听对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="监听类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择监听类型">
            <el-option
              v-for="dict in sys_listener_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="事件类型" prop="eventType" v-if="form.type === '1'">
          <el-select v-model="form.eventType" placeholder="请选择事件类型">
            <el-option
              v-for="dict in taskListenerEventList"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="事件类型" prop="eventType" v-else>
          <el-select v-model="form.eventType" placeholder="请选择事件类型">
            <el-option
              v-for="dict in executionListenerEventList"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="值类型" prop="valueType">
          <el-radio-group v-model="form.valueType">
            <el-radio
              v-for="dict in sys_listener_value_type"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="执行内容" prop="value">
          <el-input v-model="form.value" placeholder="请输入执行内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确定</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import { listListener, getListener, delListener, addListener, updateListener } from "@/api/flowable/listener";
const { proxy } = getCurrentInstance();

// 获取字典数据
const { sys_listener_value_type, sys_listener_type, common_status, sys_listener_event_type } = proxy.useDict('sys_listener_value_type', 'sys_listener_type', 'common_status', 'sys_listener_event_type');

// 初始化变量
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  name: null,
  type: null,
  eventType: null,
  valueType: null,
  value: null,
  status: null,
});
const form = reactive({
  id: null,
  name: null,
  type: null,
  eventType: null,
  valueType: null,
  value: null,
  createTime: null,
  updateTime: null,
  createBy: null,
  updateBy: null,
  status: null,
  remark: null
});
const rules = reactive({});
const listenerList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const open = ref(false);
const title = ref("");
const taskListenerEventList = ref([
  {label: 'create', value: 'create'},
  {label: 'assignment', value: 'assignment'},
  {label: 'complete', value: 'complete'},
  {label: 'delete', value: 'delete'},
]);
const executionListenerEventList = ref([
  {label: 'start', value: 'start'},
  {label: 'end', value: 'end'},
  {label: 'take', value: 'take'},
]);

// 生命周期钩子
onMounted(() => {
  getList();
});

// 方法定义
const getList = () => {
  loading.value = true;
  listListener(queryParams).then(response => {
    listenerList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
};

const cancel = () => {
  open.value = false;
  reset();
};

const reset = () => {
  form.id = null;
  form.name = null;
  form.type = null;
  form.eventType = null;
  form.valueType = null;
  form.value = null;
  form.createTime = null;
  form.updateTime = null;
  form.createBy = null;
  form.updateBy = null;
  form.status = null;
  form.remark = null;
  proxy.resetForm("form");
};

const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

const resetQuery = () => {
  proxy.resetForm("queryForm");
  handleQuery();
};

const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = selection.length === 0;
};

const handleAdd = () => {
  reset();
  open.value = true;
  title.value = "添加流程监听";
};

const handleUpdate = (row) => {
  reset();
  const id = row.id || ids.value;
  getListener(id).then(response => {
    Object.assign(form, response.data);
    open.value = true;
    title.value = "修改流程监听";
  });
};

const submitForm = () => {
  proxy.validateForm("form").then(valid => {
    if (valid) {
      if (form.id !== null) {
        updateListener(form).then(response => {
          proxy.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addListener(form).then(response => {
          proxy.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
};

const handleDelete = (row) => {
  const idsToDelete = row.id || ids.value;
  proxy.confirm('是否确认删除流程监听编号为"' + idsToDelete + '"的数据项？').then(function() {
    return delListener(idsToDelete);
  }).then(() => {
    getList();
    proxy.msgSuccess("删除成功");
  }).catch(() => {});
};

const handleExport = () => {
  proxy.download('system/listener/export', {
    ...queryParams
  }, `listener_${new Date().getTime()}.xlsx`);
};
</script>
