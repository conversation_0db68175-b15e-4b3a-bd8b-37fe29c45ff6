<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="auto"
    >
      <el-form-item label="指标名称" prop="indicatorName">
        <el-input
          v-model="queryParams.indicatorName"
          placeholder="请输入指标名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="承包商名称" prop="contractorName">
        <el-input
          v-model="queryParams.contractorName"
          placeholder="请输入承包商名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['contractor:evaluationScore:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['contractor:evaluationScore:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['contractor:evaluationScore:import']"
          >导入</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['contractor:evaluationScore:export']"
          >导出</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="importTemplate"
          >下载模板</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="evaluationScoreList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="评价任务" align="center" prop="taskId" />
      <el-table-column label="指标名称" align="center" prop="indicatorName" />
      <el-table-column label="承包商名称" align="center" prop="contractorName" />
      <el-table-column label="评分类型" align="center" prop="scoreType" />
      <el-table-column label="原始值" align="center" prop="dataValue" />
      <el-table-column label="评分" align="center" prop="score" />
      <el-table-column label="评分人姓名" align="center" prop="evaluatorName" />
      <el-table-column label="评分说明" align="center" prop="comment" />
      <el-table-column label="评分时间" align="center" prop="scoreTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.scoreTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数据来源" align="center" prop="dataSource" />
      <el-table-column label="评价任务" align="center" prop="taskName" />
      <el-table-column
        label="操作"
        width="180"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['contractor:evaluationScore:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['contractor:evaluationScore:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改评价评分结果（自动和人工）对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="evaluationScoreRef" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="评价任务ID" prop="taskId">
          <el-input v-model="form.taskId" placeholder="请输入评价任务ID" />
        </el-form-item>
        <el-form-item label="指标名称" prop="indicatorName">
          <el-input v-model="form.indicatorName" placeholder="请输入指标名称" />
        </el-form-item>
        <el-form-item label="承包商ID" prop="contractorId">
          <el-input v-model="form.contractorId" placeholder="请输入承包商ID" />
        </el-form-item>
        <el-form-item label="承包商名称" prop="contractorName">
          <el-input v-model="form.contractorName" placeholder="请输入承包商名称" />
        </el-form-item>
        <el-form-item label="评分类型" prop="scoreType">
          <el-input v-model="form.scoreType" placeholder="请输入评分类型" />
        </el-form-item>
        <el-form-item label="原始值" prop="dataValue">
          <el-input v-model="form.dataValue" placeholder="请输入原始值" />
        </el-form-item>
        <el-form-item label="评分" prop="score">
          <el-input v-model="form.score" placeholder="请输入评分" />
        </el-form-item>
        <el-form-item label="评分人" prop="evaluatorId">
          <el-input v-model="form.evaluatorId" placeholder="请输入评分人" />
        </el-form-item>
        <el-form-item label="评分人姓名" prop="evaluatorName">
          <el-input v-model="form.evaluatorName" placeholder="请输入评分人姓名" />
        </el-form-item>
        <el-form-item label="评分说明" prop="comment">
          <el-input v-model="form.comment" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="评分时间" prop="scoreTime">
          <el-date-picker
            clearable
            v-model="form.scoreTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
            placeholder="请选择评分时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="数据来源" prop="dataSource">
          <el-input v-model="form.dataSource" placeholder="请输入数据来源" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="评价任务名称" prop="taskName">
          <el-input v-model="form.taskName" placeholder="请输入评价任务名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="EvaluationScore">
import {
  listEvaluationScore,
  getEvaluationScore,
  delEvaluationScore,
  addEvaluationScore,
  updateEvaluationScore,
} from "@/api/xtcontractormag/EvaluationManage/evaluationScore";
import { deepClone, formatDate } from "@/utils";
import { getToken } from "@/utils/auth";
import useUserStore from "@/store/modules/user";

const userStore = useUserStore();

const { proxy } = getCurrentInstance();

const evaluationScoreList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const itemsArr = ref([]);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

/*** 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层
  open: false,
  // 弹出层标题
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: window.xtmapConfig.xtBaseUrl + "/contractor/evaluationScore/importData",
});

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    indicatorName: null,
    contractorName: null,
  },
  rules: {
    taskId: [{ required: true, message: "评价任务ID不能为空", trigger: "blur" }],
    indicatorName: [{ required: true, message: "指标名称不能为空", trigger: "blur" }],
    contractorId: [{ required: true, message: "承包商ID不能为空", trigger: "blur" }],
    contractorName: [{ required: true, message: "承包商名称不能为空", trigger: "blur" }],
    scoreType: [{ required: true, message: "评分类型不能为空", trigger: "blur" }],
    dataValue: [{ required: true, message: "原始值不能为空", trigger: "blur" }],
    score: [{ required: true, message: "评分不能为空", trigger: "blur" }],
    evaluatorId: [{ required: true, message: "评分人不能为空", trigger: "blur" }],
    evaluatorName: [{ required: true, message: "评分人姓名不能为空", trigger: "blur" }],
    comment: [{ required: true, message: "评分说明不能为空", trigger: "blur" }],
    scoreTime: [{ required: true, message: "评分时间不能为空", trigger: "blur" }],
    dataSource: [{ required: true, message: "数据来源不能为空", trigger: "blur" }],
    remark: [{ required: true, message: "备注不能为空", trigger: "blur" }],
    taskName: [{ required: true, message: "评价任务名称不能为空", trigger: "blur" }],
    createBy: [{ required: true, message: "创建人不能为空", trigger: "blur" }],
    createTime: [{ required: true, message: "创建时间不能为空", trigger: "blur" }],
    updateBy: [{ required: true, message: "更新人不能为空", trigger: "blur" }],
    updateTime: [{ required: true, message: "更新时间不能为空", trigger: "blur" }],
    deleted: [{ required: true, message: "是否删除不能为空", trigger: "blur" }],
    tenantId: [{ required: true, message: "租户ID不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询评价评分结果（自动和人工）列表 */
function getList() {
  loading.value = true;
  listEvaluationScore(queryParams.value).then((response) => {
    evaluationScoreList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    taskId: null,
    indicatorName: null,
    contractorId: null,
    contractorName: null,
    scoreType: null,
    dataValue: null,
    score: null,
    evaluatorId: null,
    evaluatorName: null,
    comment: null,
    scoreTime: null,
    dataSource: null,
    remark: null,
    taskName: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    deleted: null,
    tenantId: null,
  };
  proxy.resetForm("evaluationScoreRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  itemsArr.value = deepClone(selection);
  multiple.value = !selection.length;
}

/** 导入按钮操作 */
function handleImport() {
  upload.title = "导入";
  upload.open = true;
}

/** 下载模板操作 */
function importTemplate() {
  proxy.download(
    "contractor/evaluationScore/export/template",
    {},
    `evaluationScore_${new Date().getTime()}.xlsx`
  );
}

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert(
    "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
      response.msg +
      "</div>",
    "导入结果",
    { dangerouslyUseHTMLString: true }
  );
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  upload.url = `${window.xtmapConfig.xtBaseUrl}/contractor/evaluationScore/importData?userId=${userStore.id}`;
  proxy.$refs["uploadRef"].submit();
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加评价评分结果（自动和人工）";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getEvaluationScore(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改评价评分结果（自动和人工）";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["evaluationScoreRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        const data = {
          ...form.value,
          tenantId: userStore.tenantId,
          updateBy: userStore.name,
          updateTime: formatDate(new Date()),
        };
        updateEvaluationScore(data).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        const data = {
          ...form.value,
          tenantId: userStore.tenantId,
          createBy: userStore.name,
          createTime: formatDate(new Date()),
          updateBy: userStore.name,
          updateTime: formatDate(new Date()),
          deleted: "0",
        };
        addEvaluationScore(data).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id ? [row.id] : ids.value;
  proxy.$modal
    .confirm('是否确认删除评价评分结果（自动和人工）编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delEvaluationScore(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  const params = itemsArr.value.length > 0 ? itemsArr.value : undefined;
  proxy.codeDownload(
    "contractor/evaluationScore/export",
    params,
    `evaluationScore_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
