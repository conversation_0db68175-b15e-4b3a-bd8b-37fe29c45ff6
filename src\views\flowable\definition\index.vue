<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="流程名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入流程名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-upload"
          size="mini"
          @click="handleImport"
          v-hasPermi="['flowable:definition:export']"
        >导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleLoadXml"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['flowable:instance:del']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" fit :data="definitionList" border @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="流程编号" align="center" prop="deploymentId" :show-overflow-tooltip="true"/>
      <el-table-column label="流程标识" align="center" prop="flowKey" :show-overflow-tooltip="true" />
      <el-table-column label="流程分类" align="center" prop="category" />
      <el-table-column label="流程名称" align="center" width="120" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <el-button type="text" @click="handleReadImage(scope.row)">
            <span>{{ scope.row.name }}</span>
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="业务表单" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <el-button v-if="scope.row.formId" type="text" @click="handleForm(scope.row.formId)">
            <span>{{ scope.row.formName }}</span>
          </el-button>
          <label v-else>暂无表单</label>
        </template>
      </el-table-column>
      <el-table-column label="流程版本" align="center">
        <template slot-scope="scope">
          <el-tag size="medium">v{{ scope.row.version }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <el-tag type="success" v-if="scope.row.suspensionState === 1">激活</el-tag>
          <el-tag type="warning" v-if="scope.row.suspensionState === 2">挂起</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="部署时间" align="center" prop="deploymentTime" width="180"/>
      <el-table-column label="操作" width="250" fixed="right" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button @click="handleLoadXml(scope.row)" icon="el-icon-edit-outline" type="text" v-hasPermi="['flowable:definition:edit']" size="small">设计</el-button>
          <el-button @click="handleAddForm(scope.row)" icon="el-icon-edit-el-icon-s-promotion" type="text" v-hasPermi="['flowable:definition:configureform']" size="small">挂载表单</el-button>
          <el-button @click="handleUpdateSuspensionState(scope.row)" icon="el-icon-video-pause" type="text" v-hasPermi="['flowable:definition:state']" size="small" v-if="scope.row.suspensionState === 1">挂起</el-button>
          <el-button @click="handleUpdateSuspensionState(scope.row)" icon="el-icon-video-play" type="text" v-hasPermi="['flowable:definition:state']" size="small" v-if="scope.row.suspensionState === 2">激活</el-button>
          <el-button @click="handleDelete(scope.row)" icon="el-icon-delete" type="text" size="small" v-hasPermi="['flowable:definition:del']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- bpmn20.xml导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xml"
        :headers="upload.headers"
        :action="upload.url + '?name=' + upload.name+'&category='+ upload.category"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          流程名称：<el-input v-model="upload.name"/>
          流程分类：
          <div>
            <el-select v-model="upload.category" style="width: 100%" placeholder="请选择流程分类">
              <el-option
                v-for="dict in sys_process_category"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </div>
        </div>
        <div class="el-upload__tip" style="color:red" slot="tip">提示：仅允许导入“bpmn20.xml”格式文件！</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确定</el-button>
        <el-button @click="upload.open = false">取消</el-button>
      </div>
    </el-dialog>

    <!-- 流程图 -->
    <el-dialog :title="readImage.title" :visible.sync="readImage.open" width="70%" append-to-body>
      <flow :xmlData="xmlData"/>
    </el-dialog>

    <!--表单配置详情-->
    <el-dialog :title="formTitle" :visible.sync="formConfOpen" width="50%" append-to-body>
      <div class="key-form">
        <ng-form-build ref="currentFormBuild" :preview="false" :disabled="true" :formTemplate="formConf" :config="formBuildConfig"/>
      </div>
    </el-dialog>

    <!--挂载表单-->
    <el-dialog :title="formDeployTitle" :visible.sync="formDeployOpen" width="60%" append-to-body>
      <el-row :gutter="24">
        <el-col :span="10" :xs="24">
          <el-table
            ref="singleTable"
            :data="formList"
            border
            highlight-current-row
            @current-change="handleCurrentChange"
            style="width: 100%">
            <el-table-column label="表单编号" align="center" prop="formId" />
            <el-table-column label="表单名称" align="center" prop="formName" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button size="mini" type="text" @click="submitFormDeploy(scope.row)">确定</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            small
            layout="prev, pager, next"
            v-show="formTotal>0"
            :total="formTotal"
            :page.sync="formQueryParams.pageNum"
            :limit.sync="formQueryParams.pageSize"
            @pagination="ListFormDeploy"
          />
        </el-col>
        <el-col :span="14" :xs="24">
          <div v-if="currentRow">
            <ng-form-build ref="currentFormBuild" :preview="false" :disabled="true" :formTemplate="currentRow" :config="formBuildConfig" />
          </div>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted} from 'vue'
import { listDefinition, updateState, delDeployment, definitionStart, readXml } from "@/api/flowable/definition";
import { getToken } from "@/utils/auth";
import { getForm, addDeployForm, listForm } from "@/api/flowable/form";
import flow from '@/views/flowable/task/record/flow'
const router = useRouter();
const { proxy } = getCurrentInstance();
const { sys_process_category } = proxy.useDict("sys_process_category");

// 遮罩层
const loading = ref(true)
const dialogVisible = ref(false)

// 选中数组
const ids = ref([])

// 非单个禁用
const single = ref(true)

// 非多个禁用
const multiple = ref(true)

// 显示搜索条件
const showSearch = ref(true)

// 总条数
const total = ref(0)

// 流程定义表格数据
const definitionList = ref([])

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  name: null,
  category: null,
  key: null,
  tenantId: null,
  deployTime: null,
  derivedFrom: null,
  derivedFromRoot: null,
  parentDeploymentId: null,
  engineVersion: null
})

// 挂载表单到流程实例
const formDeployParam = reactive({
  formId: null,
  deployId: null
})

// 表单校验
const rules = ref({})

// 表单配置详情
const formConfOpen = ref(false)
const formTitle = ref("")
const formConf = ref({}) // 默认表单数据

// 流程图
const readImage = reactive({
  open: false,
  title: "",
  src: "",
})

// 挂载表单
const formDeployOpen = ref(false)
const formDeployTitle = ref("")
const formList = ref([])
const formTotal = ref(0)
const formQueryParams = reactive({
  pageNum: 1,
  pageSize: 10,
})

// xml
const xmlData = ref("")

// 表单构建配置
const formBuildConfig = reactive({
  httpConfig: (config) => {
    config.headers['Authorization'] = 'Bearer ' + getToken()
    return config
  }
})
const uploadRef = ref(null)
// bpmn.xml 导入
const upload = reactive({
  // 是否显示弹出层（xml导入）
  open: false,
  // 弹出层标题（xml导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  name: null,
  category: null,
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: window.xtmapConfig.xtBaseUrl + "/flowable/definition/import"
})

const currentRow = ref(null)

const getList = () => {
  loading.value = true
  listDefinition(queryParams).then(response => {
    definitionList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

const resetQuery = () => {
  resetForm("queryForm")
  handleQuery()
}

const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.deploymentId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

const handleLoadXml = (row) => {
  router.push({ path: 'model', query: { deployId: row.deploymentId } })
}

const handleReadImage = (row) => {
  readImage.title = "流程图"
  readImage.open = true
  // 发送请求，获取xml
  readXml(row.deploymentId).then(res => {
    xmlData.value = res.data
  })
}

const handleForm = (formId) => {
  getForm(formId).then(res => {
    formTitle.value = "表单详情"
    formConfOpen.value = true
    formConf.value = JSON.parse(res.data.formContent)
  })
}

const handleDefinitionStart = (row) => {
  definitionStart(row.id).then(res => {
    proxy.$modal.msgSuccess(res.msg)
  })
}

const handleAddForm = (row) => {
  formDeployParam.deployId = row.deploymentId
  ListFormDeploy()
}

const ListFormDeploy = () => {
  listForm(formQueryParams).then(res => {
    formList.value = res.rows
    formTotal.value = res.total
    formDeployOpen.value = true
    formDeployTitle.value = "挂载表单"
  })
}

const submitFormDeploy = (row) => {
  formDeployParam.formId = row.formId
  addDeployForm(formDeployParam).then(res => {
    proxy.$modal.msgSuccess(res.msg)
    formDeployOpen.value = false
    getList()
  })
}

const handleCurrentChange = (data) => {
  if (data) {
    currentRow.value = JSON.parse(data.formContent)
  }
}

const handleUpdateSuspensionState = (row) => {
  let state = 1
  if (row.suspensionState === 1) {
    state = 2
  }
  const params = {
    deployId: row.deploymentId,
    state: state
  }
  updateState(params).then(res => {
    proxy.$modal.msgSuccess(res.msg)
    getList()
  })
}

const handleDelete = (row) => {
  const deploymentIds = row.deploymentId || ids.value
  $confirm('是否确认删除流程定义编号为"' + deploymentIds + '"的数据项?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(function() {
    return delDeployment(deploymentIds)
  }).then(() => {
    getList()
     proxy.$modal.msgSuccess("删除成功");
  })
}

const handleImport = () => {
  upload.title = "bpmn20.xml文件导入"
  upload.open = true
}

const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true
}

const handleFileSuccess = (response, file, fileList) => {
  upload.open = false
  upload.isUploading = false
  uploadRef.value.clearFiles()
  $message(response.msg)
  getList()
}

const submitFileForm = () => {
  uploadRef.value.upload.submit()
}

// 生命周期钩子
onMounted(() => {
  getList()
})

</script>

<style lang="scss" scoped>
.key-form {
  width: 100%;
}
</style>
